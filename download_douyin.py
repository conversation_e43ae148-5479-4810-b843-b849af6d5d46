#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import re
import json
import os
from urllib.parse import urlparse

def download_douyin_video(url, output_dir="./downloads"):
    """
    下载抖音视频
    """
    try:
        # 创建下载目录
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 设置请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        # 获取页面内容
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        print(f"页面状态码: {response.status_code}")
        print(f"页面内容长度: {len(response.text)}")
        
        # 保存页面内容用于调试
        with open(os.path.join(output_dir, "page_content.html"), "w", encoding="utf-8") as f:
            f.write(response.text)
        
        # 尝试从页面中提取视频信息
        # 查找可能的视频URL模式
        video_patterns = [
            r'"play_addr":\s*{\s*"url_list":\s*\[(.*?)\]',
            r'"video":\s*{[^}]*"play_addr":\s*{[^}]*"url_list":\s*\[(.*?)\]',
            r'playAddr":\s*"([^"]*)"',
            r'"src":\s*"([^"]*\.mp4[^"]*)"',
        ]
        
        video_url = None
        for pattern in video_patterns:
            matches = re.findall(pattern, response.text, re.DOTALL)
            if matches:
                print(f"找到匹配模式: {pattern}")
                print(f"匹配结果: {matches[:3]}")  # 只显示前3个结果
                # 尝试解析第一个匹配结果
                match = matches[0]
                if isinstance(match, str) and ('http' in match or 'mp4' in match):
                    # 清理URL
                    if match.startswith('"') and match.endswith('"'):
                        match = match[1:-1]
                    if match.startswith('http'):
                        video_url = match
                        break
        
        if not video_url:
            print("未找到视频URL，尝试其他方法...")
            # 尝试查找所有可能的URL
            all_urls = re.findall(r'https?://[^\s"\'<>]+', response.text)
            video_urls = [url for url in all_urls if any(ext in url.lower() for ext in ['.mp4', '.m3u8', 'video'])]
            if video_urls:
                print(f"找到可能的视频URL: {video_urls[:5]}")
                video_url = video_urls[0]
        
        if video_url:
            print(f"视频URL: {video_url}")
            
            # 下载视频
            video_response = requests.get(video_url, headers=headers, stream=True, timeout=60)
            video_response.raise_for_status()
            
            # 生成文件名
            filename = f"xu_ermu_video_{url.split('/')[-1]}.mp4"
            filepath = os.path.join(output_dir, filename)
            
            # 保存视频文件
            with open(filepath, 'wb') as f:
                for chunk in video_response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            print(f"视频下载完成: {filepath}")
            return filepath
        else:
            print("未找到视频URL")
            return None
            
    except Exception as e:
        print(f"下载失败: {str(e)}")
        return None

if __name__ == "__main__":
    # 许二木的视频URL
    video_url = "https://www.douyin.com/video/7499292081387474185"
    
    print("开始下载许二木的最新视频...")
    result = download_douyin_video(video_url)
    
    if result:
        print(f"下载成功: {result}")
    else:
        print("下载失败")
