#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import os
import requests
from TikTokApi import TikTokApi

async def download_douyin_video():
    """
    使用TikTokApi下载抖音视频
    """
    try:
        # 创建下载目录
        output_dir = "./downloads"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        # 初始化TikTokApi
        api = TikTokApi()
        
        # 许二木的用户ID或视频ID
        video_id = "7499292081387474185"
        
        print(f"正在获取视频信息: {video_id}")
        
        # 获取视频信息
        video = api.video(id=video_id)
        video_data = await video.info()
        
        print(f"视频标题: {video_data.get('desc', 'Unknown')}")
        print(f"作者: {video_data.get('author', {}).get('nickname', 'Unknown')}")
        
        # 获取视频下载链接
        video_url = video_data.get('video', {}).get('downloadAddr')
        if not video_url:
            video_url = video_data.get('video', {}).get('playAddr')
        
        if video_url:
            print(f"视频URL: {video_url}")
            
            # 下载视频
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.douyin.com/',
            }
            
            response = requests.get(video_url, headers=headers, stream=True, timeout=60)
            response.raise_for_status()
            
            # 生成文件名
            filename = f"xu_ermu_video_{video_id}.mp4"
            filepath = os.path.join(output_dir, filename)
            
            # 保存视频文件
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            
            print(f"视频下载完成: {filepath}")
            return filepath
        else:
            print("未找到视频下载链接")
            return None
            
    except Exception as e:
        print(f"下载失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("开始使用TikTokApi下载许二木的最新视频...")
    result = asyncio.run(download_douyin_video())
    
    if result:
        print(f"下载成功: {result}")
    else:
        print("下载失败，可能需要其他方法")
